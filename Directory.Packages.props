<Project>
  <ItemGroup>
    <PackageVersion Include="Ardalis.GuardClauses" Version="5.0.0" />
    <PackageVersion Include="Ardalis.HttpClientTestExtensions" Version="4.2.0" />
    <PackageVersion Include="Ardalis.ListStartupServices" Version="1.1.4" />
    <PackageVersion Include="Ardalis.Result" Version="10.1.0" />
    <PackageVersion Include="Ardalis.Result.AspNetCore" Version="10.1.0" />
    <PackageVersion Include="Ardalis.SharedKernel" Version="2.1.1" />
    <PackageVersion Include="Ardalis.SmartEnum" Version="8.2.0" />
    <PackageVersion Include="Ardalis.Specification" Version="8.0.0" />
    <PackageVersion Include="Ardalis.Specification.EntityFrameworkCore" Version="8.0.0" />
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="Azure.Identity" Version="1.13.1" />
    <PackageVersion Include="Bogus" Version="35.6.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="FastEndpoints" Version="5.35.0" />
    <PackageVersion Include="FastEndpoints.ApiExplorer" Version="2.2.0" />
    <PackageVersion Include="FastEndpoints.Generator" Version="5.35.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageVersion>
    <PackageVersion Include="FastEndpoints.Swagger" Version="5.35.0" />
    <PackageVersion Include="FastEndpoints.Swagger.Swashbuckle" Version="2.2.0" />
    <PackageVersion Include="FastEndpoints.Testing" Version="5.33.0" />
    <PackageVersion Include="FluentAssertions" Version="7.1.0" />
    <PackageVersion Include="FluentValidation" Version="11.11.0" />
    <PackageVersion Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageVersion Include="MailKit" Version="4.9.0" />
    <PackageVersion Include="MediatR" Version="12.4.1" />
    <PackageVersion Include="Ardalis.ApiEndpoints" Version="4.1.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Components.Web" Version="9.0.4" />
    <PackageVersion Include="OpenTelemetry" Version="1.11.2" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="9.0.1" />
    <PackageVersion Include="Serilog.Formatting.Compact" Version="3.0.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
    <PackageVersion Include="Microsoft.AspNetCore" Version="2.3.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Components.DataAnnotations.Validation" Version="3.2.0-rc1.20223.4" />
    <PackageVersion Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.4" />
    <PackageVersion Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.4" />
    <PackageVersion Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.4" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.3" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.3" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="10.0.0-preview.3.25171.5" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.Console" Version="1.11.2" />
    <PackageVersion Include="ReportGenerator" Version="5.4.3" />
    <PackageVersion Include="Serilog" Version="4.2.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="SQLite" Version="3.13.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageVersion Include="System.Net.Http" Version="4.3.4" />
    <PackageVersion Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageVersion Include="Aspire.Hosting.AppHost" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http.Resilience" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.ServiceDiscovery" Version="9.0.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.10.0" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.10.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.10.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.10.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.10.0" />
    <PackageVersion Include="Aspire.Hosting.Testing" Version="9.0.0" />
    <PackageVersion Include="xunit.assert" Version="2.9.3" />
    <PackageVersion Include="xunit.extensibility.core" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.0.2" />
    <PackageVersion Include="xunit" Version="2.9.3" />
  </ItemGroup>
</Project>