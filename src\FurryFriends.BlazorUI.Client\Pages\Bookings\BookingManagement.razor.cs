﻿using FurryFriends.BlazorUI.Client.Models.Bookings;
using FurryFriends.BlazorUI.Client.Models.Clients;
using FurryFriends.BlazorUI.Client.Services.Interfaces;
using Microsoft.AspNetCore.Components;

namespace FurryFriends.BlazorUI.Client.Pages.Bookings;

public partial class BookingManagement
{
  [Inject] public ILogger<BookingManagement> Logger { get; set; } = default!;
  [Inject] public IClientService ClientService { get; set; } = default!;
  [Inject] public NavigationManager Navigation { get; set; } = default!;

  [Parameter] public Guid? ClientId { get; set; }

  private List<ClientDto>? availableClients;
  private ClientDto? selectedClient;
  //private string? selectedServiceArea;
  private bool showBookingForm = false;
  private bool isLoadingClients = false;
  private string? errorMessage;

  // Success modal state
  private bool showSuccessModal = false;
  private BookingResponseDto? completedBooking;

  protected override async Task OnInitializedAsync()
  {
    Logger.LogInformation("BookingManagement page initialized with ClientId: {ClientId}", ClientId);

    if (ClientId.HasValue)
    {
      await LoadSpecificClientAsync(ClientId.Value);
      showBookingForm = true;
    }
    else
    {
      await LoadClientsAsync();
    }
  }

  protected override async Task OnParametersSetAsync()
  {
    if (ClientId.HasValue && (selectedClient == null || selectedClient.Id != ClientId.Value))
    {
      await LoadSpecificClientAsync(ClientId.Value);
      showBookingForm = true;
    }
  }

  private async Task LoadClientsAsync()
  {
    try
    {
      isLoadingClients = true;
      errorMessage = null;
      StateHasChanged();

      Logger.LogInformation("Loading available clients");

      var response = await ClientService.GetClientsAsync(1, 50); // Get first 50 clients
      if (response is not null && response.RowsData != null)
      {
        availableClients = response.RowsData;
        Logger.LogInformation("Successfully loaded {Count} clients", availableClients.Count);
      }
      else
      {
        availableClients = new List<ClientDto>();
        errorMessage = "Failed to load clients";
        Logger.LogWarning("Failed to load clients: {Error}", errorMessage);
      }
    }
    catch (Exception ex)
    {
      availableClients = new List<ClientDto>();
      errorMessage = "An error occurred while loading clients";
      Logger.LogError(ex, "Error loading clients");
    }
    finally
    {
      isLoadingClients = false;
      StateHasChanged();
    }
  }

  private async Task LoadSpecificClientAsync(Guid clientId)
  {
    try
    {
      Logger.LogInformation("Loading specific client: {ClientId}", clientId);

      var response = await ClientService.GetClientAsync(clientId);
      if (response.Success && response.Data != null)
      {
        selectedClient = ClientService.MapClientDataToDto(response.Data);
        Logger.LogInformation("Successfully loaded client: {ClientName}", response.Data.Name);
      }
      else
      {
        errorMessage = response.Message ?? "Failed to load client information";
        Logger.LogWarning("Failed to load client {ClientId}: {Error}", clientId, errorMessage);
      }
    }
    catch (Exception ex)
    {
      errorMessage = "An error occurred while loading client information";
      Logger.LogError(ex, "Error loading client {ClientId}", clientId);
    }
  }

  private void StartNewBooking()
  {
    Logger.LogInformation("Starting new booking process");
    showBookingForm = false;
    selectedClient = null;
    ClientId = null;
    Navigation.NavigateTo("/bookings/new");
  }

  private void SelectClient(ClientDto client)
  {
    try
    {
      Logger.LogInformation("Client selected: {ClientId} - {ClientName}", client.Id, client.FullName);

      selectedClient = client;
      ClientId = client.Id;
      showBookingForm = true;

      // Update URL to reflect selected client
      Navigation.NavigateTo($"/bookings/new/{client.Id}");
    }
    catch (Exception ex)
    {
      Logger.LogError(ex, "Error selecting client: {ClientId}", client.Id);
    }
  }

  private void ChangeClient()
  {
    Logger.LogInformation("User requested to change client");
    showBookingForm = false;
    selectedClient = null;
    ClientId = null;
    Navigation.NavigateTo("/bookings/new");
  }

  private Task OnBookingCompleted(BookingResponseDto bookingResponse)
  {
    try
    {
      Logger.LogInformation("Booking completed successfully: {BookingId}", bookingResponse.BookingId);

      completedBooking = bookingResponse;
      showSuccessModal = true;
      StateHasChanged();

      return Task.CompletedTask;
    }
    catch (Exception ex)
    {
      Logger.LogError(ex, "Error handling booking completion");
      return Task.CompletedTask;
    }
  }

  private Task OnBookingCancelled()
  {
    try
    {
      Logger.LogInformation("Booking cancelled by user");

      // Navigate back to client selection or main bookings page
      if (ClientId.HasValue)
      {
        Navigation.NavigateTo("/bookings");
      }
      else
      {
        showBookingForm = false;
        StateHasChanged();
      }

      return Task.CompletedTask;
    }
    catch (Exception ex)
    {
      Logger.LogError(ex, "Error handling booking cancellation");
      return Task.CompletedTask;
    }
  }

  private void CloseSuccessModal()
  {
    showSuccessModal = false;
    completedBooking = null;
    StateHasChanged();
  }

  private void CreateAnotherBooking()
  {
    Logger.LogInformation("User requested to create another booking");

    CloseSuccessModal();

    // Keep the same client if one was selected
    if (selectedClient != null)
    {
      showBookingForm = true;
    }
    else
    {
      showBookingForm = false;
    }

    StateHasChanged();
  }

  private void NavigateToClients()
  {
    Logger.LogInformation("Navigating to clients management");
    Navigation.NavigateTo("/clients");
  }
}
