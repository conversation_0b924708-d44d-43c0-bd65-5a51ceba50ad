/* Component-specific styles */
.create-client-container {
    background-color: var(--card-bg, white);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    max-width: 900px;
    margin: 2rem auto;
    border: 1px solid var(--border-color, #e0e5ee);
}

.create-client-container h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-description {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

/* Override form section spacing for this specific page */
.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
}

.form-grid {
    gap: 1.5rem; /* Slightly larger gap than the default */
}

/* Direct cancel button */
.direct-cancel-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 10px;
    border-top: 1px dashed #dc3545;
}

.direct-cancel-container .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    font-weight: bold;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.direct-cancel-container .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: scale(1.05);
}

/* Responsive overrides */
@media (max-width: 768px) {
    .create-client-container {
        margin: 1rem;
        padding: 1.5rem;
    }

    .form-section {
        margin-bottom: 1.5rem;
    }
}
