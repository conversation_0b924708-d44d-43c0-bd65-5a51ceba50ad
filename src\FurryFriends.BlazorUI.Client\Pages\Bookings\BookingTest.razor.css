.booking-test-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.page-header h2 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.page-description {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

.test-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.test-controls {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-controls h4 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.component-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 20px;
    border: 2px solid;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.btn-outline-primary {
    background: transparent;
    border-color: #007bff;
    color: #007bff;
}

.btn-outline-primary:hover {
    background: #007bff;
    color: white;
}

.test-data-display {
    background: #f8fff9;
    border: 1px solid #d4edda;
    border-radius: 8px;
    padding: 20px;
}

.test-data-display h5 {
    color: #155724;
    margin-bottom: 15px;
    font-weight: 600;
}

.selection-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-item {
    color: #2c3e50;
    font-size: 0.95rem;
}

.info-item strong {
    color: #155724;
}

.component-display-area {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.component-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.component-section h4 {
    background: #f8f9fa;
    color: #495057;
    padding: 15px 20px;
    margin: 0;
    font-weight: 600;
    border-bottom: 1px solid #e9ecef;
}

.component-placeholder {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    background: #f8f9fa;
}

.debug-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
}

.debug-section h5 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.debug-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.debug-info div {
    padding: 8px 12px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.debug-info strong {
    color: #495057;
}

/* Responsive design */
@media (max-width: 768px) {
    .booking-test-container {
        padding: 15px;
    }
    
    .component-buttons {
        flex-direction: column;
    }
    
    .btn {
        text-align: center;
    }
    
    .debug-info {
        grid-template-columns: 1fr;
    }
    
    .selection-info {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .page-header h2 {
        font-size: 1.5rem;
    }
    
    .page-description {
        font-size: 1rem;
    }
    
    .component-section h4 {
        padding: 12px 15px;
        font-size: 1rem;
    }
    
    .test-controls,
    .test-data-display,
    .debug-section {
        padding: 15px;
    }
}
