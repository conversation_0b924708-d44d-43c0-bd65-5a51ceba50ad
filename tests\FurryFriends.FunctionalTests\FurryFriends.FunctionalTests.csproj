﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.HttpClientTestExtensions" />
    <PackageReference Include="coverlet.collector" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\src\FurryFriends.Core\FurryFriends.Core.csproj" />
    <ProjectReference Include="..\src\FurryFriends.Infrastructure\FurryFriends.Infrastructure.csproj" />
    <ProjectReference Include="..\src\FurryFriends.UseCases\FurryFriends.UseCases.csproj" />
    <ProjectReference Include="..\src\FurryFriends.Web\FurryFriends.Web.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
