# FurryFriends Learning Resources

## Overview
This directory contains educational resources and tutorials that use the FurryFriends solution as a practical case study for learning advanced software development techniques. These materials are designed for intermediate to advanced developers looking to enhance their skills in Blazor and .NET development.

## Available Resources

### [Blazor and .NET Masterclass](blazor-dotnet-masterclass.md)
A comprehensive guide covering advanced implementation techniques including:
- Clean Architecture implementation
- Domain-Driven Design patterns
- CQRS with MediatR
- Advanced Blazor WASM features
- Testing strategies
- Real-world implementation examples

## Prerequisites
Before starting these tutorials, you should have:
- Understanding of C# and .NET basics
- Familiarity with Blazor fundamentals
- Basic knowledge of Entity Framework
- Understanding of REST APIs
- Experience with unit testing

## How to Use These Resources

1. Start by reviewing the [technical documentation](../technical) to understand the FurryFriends solution architecture
2. Work through the masterclass sections sequentially
3. Complete the provided exercises to reinforce learning
4. Reference the code samples in the actual FurryFriends solution

## Contributing

If you'd like to contribute additional tutorials or improvements:
1. Follow the standard pull request process
2. Ensure content is clear and well-structured
3. Include practical examples from the FurryFriends codebase
4. Add appropriate diagrams where helpful

## Support

For questions or clarifications:
1. Check existing documentation first
2. Review the codebase for examples
3. Raise issues for unclear content
4. Participate in discussions

## Future Additions
We plan to add more tutorials covering:
- Advanced state management patterns
- Real-time features with SignalR
- Microservices architecture
- Cloud deployment strategies
- Performance optimization techniques