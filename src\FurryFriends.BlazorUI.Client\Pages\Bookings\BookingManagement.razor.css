.booking-management-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.header-content h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-content h1 i {
    color: #007bff;
}

.page-description {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

.header-actions .btn {
    padding: 12px 20px;
    border: 2px solid;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

/* Client Info Banner */
.client-info-banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.client-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #1565c0;
}

.client-info i {
    font-size: 1.1rem;
}

.client-info small {
    color: #1976d2;
    margin-left: 5px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

.btn-outline-secondary {
    background: transparent;
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
}

/* Loading and Error States */
.loading-container, .error-container, .no-clients-container {
    text-align: center;
    padding: 60px 20px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-radius: 8px;
    border: 1px solid;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.btn-outline-danger {
    background: transparent;
    border-color: #dc3545;
    color: #dc3545;
}

.btn-outline-danger:hover {
    background: #dc3545;
    color: white;
}

/* Empty State */
.empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.empty-state i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 15px;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 25px;
    line-height: 1.5;
}

/* Clients Grid */
.clients-grid h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.section-description {
    color: #6c757d;
    margin-bottom: 25px;
}

.clients-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.client-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.client-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    transform: translateY(-2px);
}

.client-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-grow: 1;
}

.client-avatar {
    width: 50px;
    height: 50px;
    background: #e9ecef;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.client-details h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-weight: 600;
}

.client-email, .client-phone, .client-location {
    margin: 0 0 3px 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.client-location {
    display: flex;
    align-items: center;
    gap: 5px;
}

.client-actions {
    color: #007bff;
    font-size: 1.2rem;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.client-card:hover .client-actions {
    opacity: 1;
}

/* Success Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.success-modal .modal-header {
    text-align: center;
    padding: 30px 30px 20px;
    border-bottom: 1px solid #e9ecef;
}

.success-icon {
    width: 60px;
    height: 60px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 1.5rem;
}

.success-modal .modal-header h3 {
    color: #28a745;
    margin: 0;
    font-weight: 600;
}

.modal-body {
    padding: 20px 30px;
}

.modal-body p {
    margin-bottom: 10px;
    color: #495057;
}

.success-message {
    color: #28a745;
    font-weight: 500;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding: 20px 30px 30px;
}

.btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
    .booking-management-container {
        padding: 15px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .client-info-banner {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .clients-list {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .client-card {
        padding: 15px;
    }
    
    .modal-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-content h1 {
        font-size: 1.5rem;
    }
    
    .client-info {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .client-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
    
    .success-modal .modal-header,
    .modal-body,
    .modal-actions {
        padding-left: 20px;
        padding-right: 20px;
    }
}
