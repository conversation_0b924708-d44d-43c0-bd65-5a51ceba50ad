.app-layout {
    display: flex;
    height: 100vh;
}

.sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    box-shadow: var(--box-shadow);
    z-index: 100;
}


.sidebar-header {
    padding: calc(var(--base-spacing)* 1.5) var(--base-spacing);
    border-bottom: 1px solid var(--border-color);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.main-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.top-header {
    height: 92px;
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-shrink: 0;
    box-shadow: var(--box-shadow);
}

.page-content {
    flex-grow: 1;
    padding: calc(var(--base-spacing)* 1.5);
    overflow-y: auto;
}
