@using FurryFriends.BlazorUI.Client.Pages.Clients
@using FurryFriends.BlazorUI.Client.Services.Interfaces
@rendermode InteractiveServer
@implements IDisposable
@inject IPopupService PopupService
@inject IClientService ClientService

@if (showViewPopup && !string.IsNullOrEmpty(clientEmail))
{
    <ClientViewPopup ClientEmail="@clientEmail" OnClose="HandleClose" />
}

@code {
    private bool showViewPopup = false;
    private string clientEmail = string.Empty;

    protected override void OnInitialized()
    {
        SubscribeToEvents();

        // Check if the popup should be open based on the service state
        if (PopupService.IsViewClientPopupOpen())
        {
            clientEmail = PopupService.GetCurrentClientEmail();
            showViewPopup = true;
        }
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            // Ensure we're subscribed after the component is rendered
            SubscribeToEvents();

            // Check if the popup should be open based on the service state
            if (PopupService.IsViewClientPopupOpen() && !showViewPopup)
            {
                clientEmail = PopupService.GetCurrentClientEmail();
                showViewPopup = true;
                StateHasChanged();
            }
        }
    }

    private void SubscribeToEvents()
    {
        // Unsubscribe first to avoid duplicate subscriptions
        PopupService.OnShowViewClientPopup -= ShowPopup;
        PopupService.OnCloseViewClientPopup -= ClosePopup;

        // Subscribe to events
        PopupService.OnShowViewClientPopup += ShowPopup;
        PopupService.OnCloseViewClientPopup += ClosePopup;
    }

    private void ShowPopup(string email)
    {
        clientEmail = email;
        showViewPopup = true;
        StateHasChanged();
    }

    private void ClosePopup()
    {
        showViewPopup = false;
        StateHasChanged();
    }

    private void HandleClose()
    {
        showViewPopup = false;
        StateHasChanged();

        // Notify any listeners that the popup was closed
        PopupService.CloseViewClientPopup();
    }

    public void Dispose()
    {
        try
        {
            PopupService.OnShowViewClientPopup -= ShowPopup;
            PopupService.OnCloseViewClientPopup -= ClosePopup;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during Dispose: {ex.Message}");
        }
    }
}
