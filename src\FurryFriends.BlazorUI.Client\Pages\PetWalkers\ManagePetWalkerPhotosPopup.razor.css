/* Modal-specific styles */
.modal-backdrop {
    display: block;
    background-color: rgba(0,0,0,0.5);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    animation: backdropFadeIn 0.2s ease-out;
}

@keyframes backdropFadeIn {
    from {
        background-color: rgba(0,0,0,0);
    }
    to {
        background-color: rgba(0,0,0,0.5);
    }
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 1.25rem auto;
    max-width: 1200px;
    width: 95%;
    z-index: 1050;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    border-radius: 0.3rem;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    border-radius: 0.3rem;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.modal-header-background {
    background-color: #f8f9fa;
}

.modal-title {
    margin: 0;
    font-weight: bold;
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
    max-height: calc(100vh - 150px);
    overflow-y: auto;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    opacity: 0.5;
    cursor: pointer;
    padding: 0;
    margin: 0;
}

.btn-close::after {
    content: "×";
}

.btn-close:hover {
    opacity: 1;
}

/* Bio picture styles */
.bio-picture-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: 100%;
}

.bio-image-container, .bio-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1rem;
}

.bio-image-container img, .bio-preview-container img {
    max-width: 200px;
    max-height: 200px;
    object-fit: cover;
    border-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Gallery section styles */
.gallery-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: 100%;
}

.upload-section {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1.25rem;
    margin-bottom: 1.25rem;
}

.preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.preview-item {
    width: 80px;
    height: 80px;
    border-radius: 0.25rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.status-message {
    margin-top: 0.75rem;
    font-size: 0.9rem;
    color: #495057;
}

.existing-photos-section h5 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.photos-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 1rem;
}

.no-photos-message {
    color: #6c757d;
    font-style: italic;
}

/* Photo item styles */
.position-relative {
    position: relative;
    border-radius: 0.25rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease-in-out;
    margin: 0;
}

.position-relative:hover {
    transform: scale(1.03);
}

.position-relative img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    display: block;
}

/* Delete button styling */
.btn-danger.btn-sm {
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    border-radius: 50%;
    opacity: 0.8;
    margin: 4px;
    line-height: 1;
}

.btn-danger.btn-sm:hover {
    opacity: 1;
}

/* Modal footer styling */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
}
