@using FurryFriends.BlazorUI.Client.Models.Clients

<div class="pets-section">
    <div class="pets-header">
        <h4 class="section-title">Client's Pets</h4>
    </div>
    <div class="pets-container">
        @if (IsLoading)
        {
            <div class="pets-loading-container">
                <p class="pets-loading-message">Loading pets...</p>
            </div>
        }
        else if (Pets == null || !Pets.Any())
        {
            <p class="no-pets-message">No pets found for this client.</p>
        }
        else
        {
            <div class="pet-cards-container">
                @foreach (var pet in Pets)
                {
                    <div class="pet-card">
                        <div class="pet-card-header">
                            <h4>@pet.Name</h4>
                            <span class="pet-card-species">@pet.Breed</span>
                        </div>
                        <div class="pet-card-image">
                            @if (string.IsNullOrEmpty(pet.Photo))
                            {
                                <div class="pet-image-placeholder">No image available</div>
                            }
                            else
                            {
                                <img src="@pet.Photo" alt="@pet.Name" class="pet-image" style="max-width: 200px; max-height: 200px;" />
                            }
                        </div>
                        <div class="pet-card-body">
                            <p><strong>Age:</strong> @pet.Age years</p>
                            <p><strong>Weight:</strong> @pet.Weight lbs</p>
                            <p><strong>Active:</strong> @(pet.isActive ? "Yes" : "No")</p>
                            @if (!string.IsNullOrEmpty(pet.SpecialNeeds))
                            {
                                <p><strong>Special Needs:</strong> @pet.SpecialNeeds</p>
                            }
                            @if (!string.IsNullOrEmpty(pet.MedicalConditions))
                            {
                                <p><strong>Medical:</strong> @pet.MedicalConditions</p>
                            }
                        </div>
                    </div>
                }
            </div>
        }
    </div>
</div>

@code {
    [Parameter]
    public PetDto[]? Pets { get; set; }

    [Parameter]
    public bool IsLoading { get; set; } = true;
}
