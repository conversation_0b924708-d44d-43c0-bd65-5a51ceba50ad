.booking-form-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.booking-form-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.booking-form-header h2 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.form-description {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

/* Alert Styles */
.alert {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid;
    position: relative;
}

.alert i {
    margin-right: 10px;
    font-size: 1.1rem;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.btn-close {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
}

.btn-close:hover {
    opacity: 1;
}

/* Step Indicator */
.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    position: relative;
}

.step-indicator::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #007bff;
    color: white;
}

.step.completed .step-number {
    background: #28a745;
    color: white;
}

.step-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-align: center;
    font-weight: 500;
}

.step.active .step-label {
    color: #007bff;
    font-weight: 600;
}

.step.completed .step-label {
    color: #28a745;
}

/* Step Content */
.step-content {
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.step-section {
    min-height: 400px;
}

/* Form Styles */
.booking-details-form h4 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.form-text {
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.text-muted {
    color: #6c757d;
}

/* Booking Summary */
.booking-summary {
    margin-top: 30px;
    padding: 20px;
    background: #f8fff9;
    border: 1px solid #d4edda;
    border-radius: 8px;
}

.booking-summary h5 {
    color: #155724;
    margin-bottom: 15px;
    font-weight: 600;
}

.summary-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-item .label {
    font-weight: 600;
    color: #495057;
}

.summary-item .value {
    color: #2c3e50;
}

.summary-item .value.price {
    color: #28a745;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Step Actions */
.step-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn {
    padding: 12px 24px;
    border: 2px solid;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
    border-color: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
    border-color: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
    .booking-form-container {
        padding: 15px;
        margin: 10px;
    }
    
    .step-indicator {
        flex-direction: column;
        gap: 15px;
    }
    
    .step-indicator::before {
        display: none;
    }
    
    .step {
        flex-direction: row;
        justify-content: flex-start;
        text-align: left;
    }
    
    .step-number {
        margin-right: 15px;
        margin-bottom: 0;
    }
    
    .step-content {
        padding: 20px;
    }
    
    .step-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn {
        width: 100%;
        text-align: center;
    }
    
    .summary-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .booking-form-header h2 {
        font-size: 1.5rem;
    }
    
    .form-description {
        font-size: 1rem;
    }
    
    .step-content {
        padding: 15px;
    }
    
    .step-section {
        min-height: 300px;
    }
}
