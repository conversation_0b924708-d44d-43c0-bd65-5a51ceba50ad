﻿.client-list-container {
    min-height: 100vh;
}

.client-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.add-client-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    font-weight: 500;
}

.add-icon {
    font-size: 1.2rem;
    font-weight: bold;
}

::deep body {
    background-image: url("../images/catongrass.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* Pagination styles moved to Pagination.razor.css */
