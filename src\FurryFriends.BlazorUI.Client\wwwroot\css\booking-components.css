/* ==========================================================================
   Booking Components - Shared Styles
   ========================================================================== */

/* CSS Variables for consistent theming */
:root {
    --booking-primary-color: #007bff;
    --booking-primary-hover: #0056b3;
    --booking-success-color: #28a745;
    --booking-success-hover: #218838;
    --booking-danger-color: #dc3545;
    --booking-danger-hover: #c82333;
    --booking-warning-color: #ffc107;
    --booking-warning-hover: #e0a800;
    --booking-secondary-color: #6c757d;
    --booking-secondary-hover: #545b62;
    
    --booking-text-primary: #2c3e50;
    --booking-text-secondary: #495057;
    --booking-text-muted: #6c757d;
    --booking-text-light: #f8f9fa;
    
    --booking-border-color: #e9ecef;
    --booking-border-radius: 8px;
    --booking-border-radius-lg: 12px;
    --booking-border-radius-sm: 6px;
    
    --booking-shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --booking-shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --booking-shadow-lg: 0 10px 30px rgba(0,0,0,0.3);
    
    --booking-spacing-xs: 5px;
    --booking-spacing-sm: 10px;
    --booking-spacing-md: 15px;
    --booking-spacing-lg: 20px;
    --booking-spacing-xl: 30px;
    
    --booking-font-size-sm: 0.875rem;
    --booking-font-size-base: 1rem;
    --booking-font-size-lg: 1.1rem;
    --booking-font-size-xl: 1.25rem;
    
    --booking-transition: all 0.3s ease;
}

/* ==========================================================================
   Base Booking Styles
   ========================================================================== */

.booking-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.5;
    color: var(--booking-text-primary);
}

/* ==========================================================================
   Button Styles
   ========================================================================== */

.booking-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--booking-spacing-xs);
    padding: 12px 20px;
    border: 2px solid;
    border-radius: var(--booking-border-radius-sm);
    font-weight: 600;
    font-size: var(--booking-font-size-base);
    text-decoration: none;
    cursor: pointer;
    transition: var(--booking-transition);
    user-select: none;
}

.booking-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.booking-btn-primary {
    background: var(--booking-primary-color);
    border-color: var(--booking-primary-color);
    color: white;
}

.booking-btn-primary:hover:not(:disabled) {
    background: var(--booking-primary-hover);
    border-color: var(--booking-primary-hover);
}

.booking-btn-success {
    background: var(--booking-success-color);
    border-color: var(--booking-success-color);
    color: white;
}

.booking-btn-success:hover:not(:disabled) {
    background: var(--booking-success-hover);
    border-color: var(--booking-success-hover);
}

.booking-btn-secondary {
    background: var(--booking-secondary-color);
    border-color: var(--booking-secondary-color);
    color: white;
}

.booking-btn-secondary:hover:not(:disabled) {
    background: var(--booking-secondary-hover);
    border-color: var(--booking-secondary-hover);
}

.booking-btn-outline-primary {
    background: transparent;
    border-color: var(--booking-primary-color);
    color: var(--booking-primary-color);
}

.booking-btn-outline-primary:hover:not(:disabled) {
    background: var(--booking-primary-color);
    color: white;
}

.booking-btn-sm {
    padding: 8px 16px;
    font-size: var(--booking-font-size-sm);
}

.booking-btn-lg {
    padding: 16px 24px;
    font-size: var(--booking-font-size-lg);
}

/* ==========================================================================
   Form Styles
   ========================================================================== */

.booking-form-group {
    margin-bottom: var(--booking-spacing-lg);
}

.booking-form-label {
    display: block;
    margin-bottom: var(--booking-spacing-xs);
    font-weight: 600;
    color: var(--booking-text-secondary);
}

.booking-form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--booking-border-color);
    border-radius: var(--booking-border-radius-sm);
    font-size: var(--booking-font-size-base);
    transition: var(--booking-transition);
    background: white;
}

.booking-form-control:focus {
    outline: none;
    border-color: var(--booking-primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.booking-form-control.is-invalid {
    border-color: var(--booking-danger-color);
}

.booking-invalid-feedback {
    color: var(--booking-danger-color);
    font-size: var(--booking-font-size-sm);
    margin-top: var(--booking-spacing-xs);
    display: block;
}

.booking-form-text {
    font-size: var(--booking-font-size-sm);
    color: var(--booking-text-muted);
    margin-top: var(--booking-spacing-xs);
    display: block;
}

/* ==========================================================================
   Card Styles
   ========================================================================== */

.booking-card {
    background: white;
    border: 2px solid var(--booking-border-color);
    border-radius: var(--booking-border-radius);
    padding: var(--booking-spacing-lg);
    box-shadow: var(--booking-shadow-sm);
    transition: var(--booking-transition);
}

.booking-card:hover {
    border-color: var(--booking-primary-color);
    box-shadow: var(--booking-shadow-md);
    transform: translateY(-2px);
}

.booking-card-header {
    margin-bottom: var(--booking-spacing-md);
    padding-bottom: var(--booking-spacing-md);
    border-bottom: 1px solid var(--booking-border-color);
}

.booking-card-title {
    margin: 0;
    color: var(--booking-text-primary);
    font-weight: 600;
}

.booking-card-body {
    flex-grow: 1;
}

.booking-card-footer {
    margin-top: var(--booking-spacing-md);
    padding-top: var(--booking-spacing-md);
    border-top: 1px solid var(--booking-border-color);
}

/* ==========================================================================
   Alert Styles
   ========================================================================== */

.booking-alert {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: var(--booking-spacing-lg);
    border-radius: var(--booking-border-radius);
    border: 1px solid;
    position: relative;
}

.booking-alert i {
    margin-right: var(--booking-spacing-sm);
    font-size: var(--booking-font-size-lg);
}

.booking-alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.booking-alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.booking-alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.booking-alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.booking-alert-dismissible {
    padding-right: 50px;
}

.booking-alert-close {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.booking-alert-close:hover {
    opacity: 1;
}

/* ==========================================================================
   Badge Styles
   ========================================================================== */

.booking-badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 12px;
    text-align: center;
    white-space: nowrap;
}

.booking-badge-success {
    background: #d4edda;
    color: #155724;
}

.booking-badge-primary {
    background: #d1ecf1;
    color: #0c5460;
}

.booking-badge-warning {
    background: #fff3cd;
    color: #856404;
}

.booking-badge-danger {
    background: #f8d7da;
    color: #721c24;
}

/* ==========================================================================
   Loading Spinner
   ========================================================================== */

.booking-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--booking-primary-color);
    border-radius: 50%;
    animation: booking-spin 1s linear infinite;
    margin: 0 auto;
}

.booking-spinner-sm {
    width: 20px;
    height: 20px;
    border-width: 2px;
}

.booking-spinner-lg {
    width: 60px;
    height: 60px;
    border-width: 6px;
}

@keyframes booking-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.booking-text-center { text-align: center; }
.booking-text-left { text-align: left; }
.booking-text-right { text-align: right; }

.booking-text-primary { color: var(--booking-text-primary); }
.booking-text-secondary { color: var(--booking-text-secondary); }
.booking-text-muted { color: var(--booking-text-muted); }
.booking-text-success { color: var(--booking-success-color); }
.booking-text-danger { color: var(--booking-danger-color); }
.booking-text-warning { color: var(--booking-warning-color); }

.booking-bg-light { background-color: #f8f9fa; }
.booking-bg-white { background-color: white; }

.booking-d-flex { display: flex; }
.booking-d-block { display: block; }
.booking-d-inline-block { display: inline-block; }
.booking-d-none { display: none; }

.booking-justify-content-center { justify-content: center; }
.booking-justify-content-between { justify-content: space-between; }
.booking-justify-content-end { justify-content: flex-end; }

.booking-align-items-center { align-items: center; }
.booking-align-items-start { align-items: flex-start; }
.booking-align-items-end { align-items: flex-end; }

.booking-gap-xs { gap: var(--booking-spacing-xs); }
.booking-gap-sm { gap: var(--booking-spacing-sm); }
.booking-gap-md { gap: var(--booking-spacing-md); }
.booking-gap-lg { gap: var(--booking-spacing-lg); }

.booking-m-0 { margin: 0; }
.booking-mb-sm { margin-bottom: var(--booking-spacing-sm); }
.booking-mb-md { margin-bottom: var(--booking-spacing-md); }
.booking-mb-lg { margin-bottom: var(--booking-spacing-lg); }

.booking-p-0 { padding: 0; }
.booking-p-sm { padding: var(--booking-spacing-sm); }
.booking-p-md { padding: var(--booking-spacing-md); }
.booking-p-lg { padding: var(--booking-spacing-lg); }
