.booking-confirmation-container {
    max-width: 700px;
    margin: 0 auto;
}

.confirmation-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.confirmation-icon {
    width: 60px;
    height: 60px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 1.5rem;
}

.confirmation-header h3 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 600;
}

.confirmation-subtitle {
    color: #6c757d;
    margin: 0;
    font-size: 1rem;
}

.confirmation-content {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.confirmation-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.confirmation-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.confirmation-section h4 i {
    color: #007bff;
    width: 20px;
}

/* Pet <PERSON> Info */
.petwalker-info {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.petwalker-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.petwalker-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.petwalker-avatar.placeholder {
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 1.5rem;
}

.petwalker-details h5 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-weight: 600;
}

.petwalker-details .email {
    color: #6c757d;
    margin: 0 0 10px 0;
    font-size: 0.9rem;
}

.petwalker-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 8px;
}

.badge {
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: 500;
}

.badge.verified {
    background: #d4edda;
    color: #155724;
}

.badge.insured {
    background: #d1ecf1;
    color: #0c5460;
}

.badge.first-aid {
    background: #f8d7da;
    color: #721c24;
}

.experience {
    color: #495057;
    margin: 0;
    font-size: 0.9rem;
}

/* Pet Info */
.pet-info {
    padding: 10px 0;
}

.pet-details h5 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-weight: 600;
}

.pet-breed {
    color: #6c757d;
    margin: 0 0 8px 0;
    font-size: 0.9rem;
}

.pet-description {
    color: #495057;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.no-pet {
    color: #dc3545;
    margin: 0;
    font-style: italic;
}

/* Booking Details */
.booking-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-weight: 600;
    color: #495057;
}

.detail-row .value {
    color: #2c3e50;
}

/* Special Instructions */
.special-instructions {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

.special-instructions p {
    margin: 0;
    color: #495057;
    line-height: 1.5;
}

/* Price Summary */
.price-summary {
    border-left-color: #28a745;
}

.price-breakdown {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.price-row.total {
    border-top: 2px solid #28a745;
    padding-top: 15px;
    margin-top: 10px;
    font-weight: 600;
    font-size: 1.1rem;
}

.price-row.total .value {
    color: #28a745;
    font-size: 1.2rem;
}

/* Important Notes */
.important-notes {
    border-left-color: #ffc107;
}

.notes-list {
    margin: 0;
    padding-left: 20px;
    color: #495057;
}

.notes-list li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.notes-list li:last-child {
    margin-bottom: 0;
}

/* Action Buttons */
.confirmation-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn {
    padding: 12px 24px;
    border: 2px solid;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #218838;
    border-color: #218838;
}

.btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
    border-color: #545b62;
}

.spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: 0.125em;
}

.spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-width: 0.125em;
}

.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .booking-confirmation-container {
        padding: 0 10px;
    }
    
    .confirmation-section {
        padding: 15px;
    }
    
    .petwalker-info {
        flex-direction: column;
        text-align: center;
    }
    
    .petwalker-avatar {
        align-self: center;
    }
    
    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .price-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .confirmation-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .confirmation-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .confirmation-header h3 {
        font-size: 1.3rem;
    }
    
    .confirmation-section {
        padding: 12px;
    }
    
    .confirmation-section h4 {
        font-size: 1rem;
    }
}
