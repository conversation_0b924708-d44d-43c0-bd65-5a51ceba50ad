@page "/bookings"
@page "/bookings/new"
@page "/bookings/new/{ClientId:guid}"
@rendermode InteractiveAuto
@using FurryFriends.BlazorUI.Client.Components.Bookings
@using FurryFriends.BlazorUI.Client.Models.Bookings
@using FurryFriends.BlazorUI.Client.Models.Clients
@using FurryFriends.BlazorUI.Client.Services.Interfaces
@using Microsoft.Extensions.Logging

@inject ILogger<BookingManagement> _logger
@inject IClientService _clientService
@inject NavigationManager _navigation

<PageTitle>Booking Management - FurryFriends</PageTitle>

<div class="booking-management-container">
    <div class="page-header">
        <div class="header-content">
            <h1>
                <i class="fas fa-calendar-plus"></i>
                @if (showBookingForm)
                {
                    <span>Create New Booking</span>
                }
                else
                {
                    <span>Booking Management</span>
                }
            </h1>
            <p class="page-description">
                @if (showBookingForm)
                {
                    <span>Book a professional pet walker for your furry friend</span>
                }
                else
                {
                    <span>Manage your pet walking bookings and schedule new appointments</span>
                }
            </p>
        </div>
        
        @if (!showBookingForm)
        {
            <div class="header-actions">
                <button class="btn btn-primary" @onclick="StartNewBooking">
                    <i class="fas fa-plus"></i>
                    New Booking
                </button>
            </div>
        }
    </div>

    @if (showBookingForm)
    {
        <!-- Booking Form Section -->
        <div class="booking-form-section">
            @if (selectedClient != null)
            {
                <div class="client-info-banner">
                    <div class="client-info">
                        <i class="fas fa-user"></i>
                        <span>Booking for: <strong>@selectedClient.FullName</strong></span>
                        <small>(@selectedClient.EmailAddress)</small>
                    </div>
                    <button class="btn btn-sm btn-outline-secondary" @onclick="ChangeClient">
                        <i class="fas fa-exchange-alt"></i>
                        Change Client
                    </button>
                </div>
            }

            <BookingFormComponent 
                ClientId="@ClientId"
                @* ServiceArea="@selectedServiceArea" *@
                OnBookingCompleted="OnBookingCompleted"
                OnBookingCancelled="OnBookingCancelled" />
        </div>
    }
    else
    {
        <!-- Client Selection Section -->
        <div class="client-selection-section">
            @if (isLoadingClients)
            {
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <p>Loading clients...</p>
                </div>
            }
            else if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="error-container">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>@errorMessage</span>
                        <button class="btn btn-sm btn-outline-danger" @onclick="LoadClientsAsync">
                            <i class="fas fa-redo"></i>
                            Retry
                        </button>
                    </div>
                </div>
            }
            else if (availableClients == null || !availableClients.Any())
            {
                <div class="no-clients-container">
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <h3>No Clients Found</h3>
                        <p>You need to have clients registered before creating bookings.</p>
                        <button class="btn btn-primary" @onclick="NavigateToClients">
                            <i class="fas fa-user-plus"></i>
                            Manage Clients
                        </button>
                    </div>
                </div>
            }
            else
            {
                <div class="clients-grid">
                    <h3>Select a Client</h3>
                    <p class="section-description">Choose the client for whom you want to create a booking</p>
                    
                    <div class="clients-list">
                        @foreach (var client in availableClients)
                        {
                            <div class="client-card" @onclick="() => SelectClient(client)">
                                <div class="client-info">
                                    <div class="client-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="client-details">
                                        <h4>@client.FullName</h4>
                                        <p class="client-email">@client.EmailAddress</p>
                                        <p class="client-phone">@client.PhoneNumber</p>
                                        @if (!string.IsNullOrEmpty(client.City))
                                        {
                                            <p class="client-location">
                                                <i class="fas fa-map-marker-alt"></i>
                                                @client.City
                                            </p>
                                        }
                                    </div>
                                </div>
                                <div class="client-actions">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    }

    <!-- Success Modal -->
    @if (showSuccessModal)
    {
        <div class="modal-overlay" @onclick="CloseSuccessModal">
            <div class="modal-content success-modal" @onclick:stopPropagation="true">
                <div class="modal-header">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>Booking Created Successfully!</h3>
                </div>
                <div class="modal-body">
                    @if (completedBooking != null)
                    {
                        <p>Your booking has been created with ID: <strong>@completedBooking.BookingId</strong></p>
                        <p>Date: <strong>@completedBooking.StartDate.ToString("dddd, MMMM dd, yyyy")</strong></p>
                        <p>Time: <strong>@completedBooking.StartDate.ToString("HH:mm") - @completedBooking.EndDate.ToString("HH:mm")</strong></p>
                    }
                    <p class="success-message">@completedBooking?.Message</p>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-primary" @onclick="CreateAnotherBooking">
                        <i class="fas fa-plus"></i>
                        Create Another Booking
                    </button>
                    <button class="btn btn-secondary" @onclick="CloseSuccessModal">
                        <i class="fas fa-times"></i>
                        Close
                    </button>
                </div>
            </div>
        </div>
    }
</div>
