.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.popup-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    width: 80%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.popup-header h3 {
    margin: 0;
    font-weight: bold;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.popup-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.loading-container, .error-container {
    padding: 20px;
    text-align: center;
}

.error-container {
    color: #dc3545;
}

.client-form-section {
    width: 100%;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 15px 0 0 0;
    border-top: 1px solid #dee2e6;
    margin-top: 20px;
}
