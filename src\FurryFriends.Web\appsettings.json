﻿{
    "ConnectionStrings": {
        "DefaultConnection": "Server=(localdb)\\v11.0;Database=cleanarchitecture;Trusted_Connection=True;MultipleActiveResultSets=true",
        "SqliteConnection": "Data Source=database.sqlite",
        "FurryFriendsSqlConnection": "Server=.;Database=FurryFriends;Trusted_Connection=True;MultipleActiveResultSets=true; TrustServerCertificate=True;"
    },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
            "path": "Logs\\log-.txt",
            "rollingInterval": "Day",
            "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}",
            "rollOnFileSizeLimit": true
        }
      }
      //Uncomment this section if you'd like to push your logs to Azure Application Insights
      //Full list of Serilog Sinks can be found here: https://github.com/serilog/serilog/wiki/Provided-Sinks
      //{
      //  "Name": "ApplicationInsights",
      //  "Args": {
      //    "instrumentationKey": "", //Fill in with your ApplicationInsights InstrumentationKey
      //    "telemetryConverter": "Serilog.Sinks.ApplicationInsights.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
      //  }
      //}
    ]
  },
  "Mailserver": {
    "Server": "localhost",
    "Port": 25
  }
}
