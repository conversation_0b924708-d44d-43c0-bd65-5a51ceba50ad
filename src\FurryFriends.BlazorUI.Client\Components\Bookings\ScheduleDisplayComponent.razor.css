.schedule-display-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.schedule-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
}

.schedule-header h4 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-weight: 600;
}

.selected-date {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.loading-container, .error-container, .no-schedule-container {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-spinner.small {
    width: 20px;
    height: 20px;
    border-width: 2px;
    margin: 0 10px 0 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    color: #dc3545;
    margin-bottom: 15px;
}

.schedule-view {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.weekly-schedule h5, .daily-detail h5 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.day-column {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 120px;
}

.day-column:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
}

.day-column.selected-day {
    border-color: #28a745;
    background: #f8fff9;
    box-shadow: 0 2px 8px rgba(40,167,69,0.2);
}

.day-header {
    text-align: center;
    margin-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 5px;
}

.day-name {
    font-weight: 600;
    color: #495057;
    display: block;
    font-size: 0.9rem;
}

.day-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.day-schedule {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.time-slot {
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    text-align: center;
}

.time-slot.available {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.no-availability {
    text-align: center;
    color: #6c757d;
    font-size: 0.8rem;
    font-style: italic;
    padding: 10px 0;
}

.daily-detail {
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
}

.loading-slots {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #6c757d;
}

.time-slots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.time-slot-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.time-slot-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
    transform: translateY(-1px);
}

.time-slot-card.selected {
    border-color: #28a745;
    background: #f8fff9;
    box-shadow: 0 2px 8px rgba(40,167,69,0.2);
}

.slot-time {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
    margin-bottom: 5px;
}

.slot-duration {
    color: #6c757d;
    font-size: 0.85rem;
    margin-bottom: 5px;
}

.slot-price {
    color: #28a745;
    font-weight: 600;
    font-size: 0.9rem;
}

.no-slots, .select-date-prompt {
    text-align: center;
    padding: 30px;
    color: #6c757d;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    .days-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
    }
    
    .day-column {
        padding: 8px;
        min-height: 100px;
    }
    
    .day-name {
        font-size: 0.8rem;
    }
    
    .day-date {
        font-size: 0.7rem;
    }
    
    .time-slot {
        font-size: 0.7rem;
        padding: 3px 5px;
    }
    
    .time-slots-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .time-slot-card {
        padding: 10px;
    }
    
    .slot-time {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .days-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .schedule-display-container {
        padding: 15px;
    }
    
    .time-slots-grid {
        grid-template-columns: 1fr;
    }
}
