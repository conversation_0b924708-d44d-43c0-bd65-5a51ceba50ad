﻿using FurryFriends.Core.BookingAggregate;

namespace FurryFriends.Infrastructure.Data.Config;

public class BookingConfiguration : IEntityTypeConfiguration<Booking>
{
  public void Configure(EntityTypeBuilder<Booking> builder)
  {
    builder.ToTable("Bookings");

    builder.HasKey(b => b.Id);

    builder.Property(b => b.Id)
        .ValueGeneratedOnAdd()
        .IsRequired();

    builder.Property(b => b.<PERSON>d)
        .IsRequired();

    builder.Property(b => b.PetOwnerId)
        .IsRequired();

    builder.Property(b => b.StartTime)
        .IsRequired();

    builder.Property(b => b.EndTime)
        .IsRequired();
    builder.Property(b => b.Price)
      .HasColumnType("decimal(18,2)")
      .HasDefaultValue(0.0m)
       .IsRequired();

    builder.Property(b => b.Status)
        .HasConversion<int>()
        .IsRequired();

    builder.Property(b => b.Notes);

    builder.HasOne(b => b.<PERSON>)
        .WithMany()
        .HasForeignKey(b => b.<PERSON>alker<PERSON>d)
        .OnDelete(DeleteBehavior.Restrict);

    builder.HasOne(b => b.PetOwner)
        .WithMany()
        .HasForeignKey(b => b.PetOwnerId)
        .OnDelete(DeleteBehavior.Restrict);
  }
}
