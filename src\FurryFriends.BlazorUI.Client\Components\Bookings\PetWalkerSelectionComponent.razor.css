.petwalker-selection-container {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.selection-header {
    margin-bottom: 20px;
    text-align: center;
}

.selection-header h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.service-area-filter {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.loading-container, .error-container, .no-walkers-container {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    color: #dc3545;
    margin-bottom: 15px;
}

.petwalkers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.petwalker-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.petwalker-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    transform: translateY(-2px);
}

.petwalker-card.selected {
    border-color: #28a745;
    background: #f8fff9;
    box-shadow: 0 4px 12px rgba(40,167,69,0.2);
}

.petwalker-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
}

.profile-picture {
    flex-shrink: 0;
}

.profile-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e9ecef;
}

.profile-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 24px;
}

.petwalker-info {
    flex-grow: 1;
}

.petwalker-name {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.petwalker-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.badge {
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: 500;
}

.badge.verified {
    background: #d4edda;
    color: #155724;
}

.badge.insured {
    background: #d1ecf1;
    color: #0c5460;
}

.badge.first-aid {
    background: #f8d7da;
    color: #721c24;
}

.petwalker-rate {
    text-align: right;
    flex-shrink: 0;
}

.rate-amount {
    font-size: 1.2rem;
    font-weight: 700;
    color: #28a745;
    display: block;
}

.rate-period {
    font-size: 0.8rem;
    color: #6c757d;
}

.petwalker-details {
    margin-top: 15px;
}

.biography {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 10px;
}

.experience-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #495057;
    margin-bottom: 8px;
}

.service-areas {
    margin-bottom: 10px;
}

.service-areas small {
    color: #6c757d;
    font-size: 0.8rem;
}

.rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
}

.stars {
    display: flex;
    gap: 2px;
}

.stars i {
    color: #ddd;
    font-size: 0.9rem;
}

.stars i.filled {
    color: #ffc107;
}

.rating-text {
    font-size: 0.85rem;
    color: #6c757d;
}

.selected-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    color: #28a745;
    font-size: 0.85rem;
    font-weight: 600;
}

.selected-indicator i {
    font-size: 1.1rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .petwalkers-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .petwalker-card {
        padding: 15px;
    }
    
    .petwalker-header {
        gap: 10px;
    }
    
    .profile-img, .profile-placeholder {
        width: 50px;
        height: 50px;
    }
    
    .experience-info {
        flex-direction: column;
        gap: 4px;
    }
}
