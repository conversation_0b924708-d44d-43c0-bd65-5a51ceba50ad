﻿::deep body {
    background-image: url("../../images/OIP.jpeg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.home-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.hero-section {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9));
    border-radius: var(--border-radius);
    margin-bottom: 3rem;
}

.hero-section h1 {
    font-size: 2.5rem;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-color);
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.services-section, .features-section {
    margin-bottom: 3rem;
}

.service-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-card {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: transform 0.2s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    text-align: center;
    padding: 1.5rem;
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.process-section {
    margin-bottom: 3rem;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.step-card {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    position: relative;
}

.step-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.walker-callout {
    background: var(--secondary-bg);
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    margin: 3rem 0;
    text-align: center;
}

.walker-benefits {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.walker-benefits li {
    font-size: 1.1rem;
    color: var(--text-color);
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.cta-section {
    text-align: center;
    padding: 3rem;
    background: var(--primary-color);
    color: var(--light-text-color);
    border-radius: var(--border-radius);
    margin-top: 3rem;
}

.cta-section h2 {
    color: var(--light-text-color);
    margin-bottom: 1rem;
}

.btn {
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--light-text-color);
    border: none;
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 1rem;
    }

    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
    }

    .service-cards, .features-grid {
        grid-template-columns: 1fr;
    }

    .walker-benefits {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
    }
}
