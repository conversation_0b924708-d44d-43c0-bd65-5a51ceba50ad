﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ScopedCssEnabled>true</ScopedCssEnabled>
  </PropertyGroup>


 <ItemGroup>
   <ProjectReference Include="..\FurryFriends.BlazorUI.Client\FurryFriends.BlazorUI.Client.csproj" />
   <ProjectReference Include="..\FurryFriends.ServiceDefaults\FurryFriends.ServiceDefaults.csproj" 
                     IsAspireProjectResource="false" />
 </ItemGroup>


 <ItemGroup>
   <Folder Include="Models\" />
   <Folder Include="wwwroot\css\" />
   <Folder Include="wwwroot\images\" />
 </ItemGroup>


 <ItemGroup>
   <PackageReference Include="Microsoft.AspNetCore.Components.Web" />
   <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly"  />
   <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" />
   <PackageReference Include="Serilog.AspNetCore" />
   <PackageReference Include="Serilog.Sinks.File" />
 </ItemGroup>
</Project>
