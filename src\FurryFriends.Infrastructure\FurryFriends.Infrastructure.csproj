﻿<Project Sdk="Microsoft.NET.Sdk">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />

  <ItemGroup>
    <PackageReference Include="Ardalis.SharedKernel" />
    <PackageReference Include="Ardalis.Specification.EntityFrameworkCore" />
    <PackageReference Include="Azure.Identity" />
    <PackageReference Include="MailKit" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" PrivateAssets="all" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="SQLite" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\FurryFriends.Core\FurryFriends.Core.csproj" />
    <ProjectReference Include="..\FurryFriends.UseCases\FurryFriends.UseCases.csproj" />
  </ItemGroup>


  <ItemGroup>
    <Folder Include="Data\Queries\" />
  </ItemGroup>
</Project>
