/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 1rem;
    gap: 1rem;
    font-size: 0.9rem;
    padding: 10px 0;
    border-top: 1px solid #dee2e6;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #777;
}

.page-size-selector select {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.pagination-info {
    color: #777;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.current-page {
    padding: 0 0.5rem;
    display: inline-block;
    min-width: 40px;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        align-items: flex-end;
        gap: 0.5rem;
    }
}
