﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />
  <PropertyGroup>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <OutputType>Exe</OutputType>
    <WebProjectMode>true</WebProjectMode>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.ListStartupServices" />
    <PackageReference Include="Ardalis.Result" />
    <PackageReference Include="Ardalis.Result.AspNetCore" />
    <PackageReference Include="FastEndpoints" />
    <PackageReference Include="FastEndpoints.Generator">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="FastEndpoints.Swagger" />
    <PackageReference Include="FluentValidation.AspNetCore" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Ardalis.ApiEndpoints" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FurryFriends.Infrastructure\FurryFriends.Infrastructure.csproj" />
    <ProjectReference Include="..\FurryFriends.UseCases\FurryFriends.UseCases.csproj" />
    <ProjectReference Include="..\FurryFriends.ServiceDefaults\FurryFriends.ServiceDefaults.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Logs\" />
  </ItemGroup>

</Project>
