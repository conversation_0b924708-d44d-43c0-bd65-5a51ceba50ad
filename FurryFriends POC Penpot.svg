<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440" height="900" viewBox="0 0 1440 900" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <style>
            /* Define colors based on CSS Variables */
            :root {
                --primary-color: #4A90E2;
                --secondary-color: #50E3C2;
                --text-color: #4A4A4A;
                --light-text-color: #ffffff;
                --bg-color: #F7F8FC;
                --sidebar-bg: #ffffff;
                --header-bg: #ffffff;
                --card-bg: #ffffff;
                --border-color: #E0E5EE;
                --hover-bg: #f0f4f8;
                --active-bg: #e6effc;
                --active-border: var(--primary-color);
                --placeholder-text: #9B9B9B;
                --table-header-bg: #f8f9fa;
                --table-header-text: #555;
                --danger-color: #dc3545;
            }

            /* Basic Font Styles (Approximation) */
            .font-main { font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; }
            .text-base { font-size: 14px; fill: var(--text-color); }
            .text-small { font-size: 12px; }
            .text-large { font-size: 18px; }
            .text-xl { font-size: 24px; } /* Approximation for h1 */
            .text-lg { font-size: 20px; } /* Approximation for h2/h3 */
            .font-semibold { font-weight: 600; }
            .font-medium { font-weight: 500; }
            .text-light-grey { fill: var(--placeholder-text); }
            .text-white { fill: var(--light-text-color); }
            .text-primary { fill: var(--primary-color); }
            .uppercase { text-transform: uppercase; }
            .text-center { text-anchor: middle; }
            .text-end { text-anchor: end; }

            /* Sidebar specific */
            .sidebar-nav-item:hover > rect { fill: var(--hover-bg); }
            .sidebar-nav-item.active > rect.bg { fill: var(--active-bg); }
            .sidebar-nav-item.active > rect.border { visibility: visible; }
            .sidebar-nav-item.active > text { fill: var(--primary-color); font-weight: 600; }
            .sidebar-nav-item > rect.border { visibility: hidden; }

        </style>
    </defs>

    <!-- Main Background -->
    <rect x="0" y="0" width="1440" height="900" fill="var(--bg-color)" class="font-main"/>

    <!-- === Sidebar === -->
    <g id="sidebar">
        <rect x="0" y="0" width="240" height="900" fill="var(--sidebar-bg)" stroke="var(--border-color)" stroke-width="1"/>

        <!-- Sidebar Header -->
        <g id="sidebar-header" transform="translate(20, 20)">
            <text x="30" y="20" class="text-primary font-semibold" style="font-size: 24px;">🐾</text>
            <text x="65" y="23" class="text-primary font-semibold" style="font-size: 20px;">FurryFriends</text>
            <line x1="0" y1="50" x2="200" y2="50" stroke="var(--border-color)" stroke-width="1"/>
        </g>

        <!-- Sidebar Navigation -->
        <g id="sidebar-nav" transform="translate(0, 90)">
            <!-- Clients Section -->
            <text x="20" y="0" class="text-light-grey font-semibold uppercase text-small" style="letter-spacing: 0.5px;">Clients</text>
            <g id="nav-list-clients" class="sidebar-nav-item active" transform="translate(0, 20)">
                 <rect class="bg" x="0" y="0" width="240" height="40" fill="var(--active-bg)"/>
                 <rect class="border" x="0" y="0" width="3" height="40" fill="var(--active-border)"/>
                 <text x="25" y="25" class="icon text-lg">📄</text>
                 <text x="55" y="26" class="text-base font-medium text-primary">List Clients</text>
            </g>
            <g id="nav-add-client" class="sidebar-nav-item" transform="translate(0, 62)">
                 <rect class="bg" x="0" y="0" width="240" height="40" fill="transparent"/>
                 <rect class="border" x="0" y="0" width="3" height="40" fill="var(--active-border)"/>
                 <text x="25" y="25" class="icon text-lg">➕</text>
                 <text x="55" y="26" class="text-base font-medium">Add New Client</text>
            </g>

             <!-- Other Section -->
             <text x="20" y="125" class="text-light-grey font-semibold uppercase text-small" style="letter-spacing: 0.5px;">Other</text>
             <g id="nav-dashboard" class="sidebar-nav-item" transform="translate(0, 145)">
                 <rect class="bg" x="0" y="0" width="240" height="40" fill="transparent"/>
                 <rect class="border" x="0" y="0" width="3" height="40" fill="var(--active-border)"/>
                 <text x="25" y="25" class="icon text-lg">📊</text>
                 <text x="55" y="26" class="text-base font-medium">Dashboard</text>
             </g>
             <g id="nav-settings" class="sidebar-nav-item" transform="translate(0, 187)">
                 <rect class="bg" x="0" y="0" width="240" height="40" fill="transparent"/>
                 <rect class="border" x="0" y="0" width="3" height="40" fill="var(--active-border)"/>
                 <text x="25" y="25" class="icon text-lg">⚙️</text>
                 <text x="55" y="26" class="text-base font-medium">Settings</text>
             </g>
        </g>

         <!-- Sidebar Footer -->
        <g id="sidebar-footer" transform="translate(20, 850)">
            <line x1="0" y1="-10" x2="200" y2="-10" stroke="var(--border-color)" stroke-width="1"/>
            <text y="10" class="text-light-grey text-small">Version 1.0.0</text>
        </g>
    </g>

    <!-- === Main Content Area === -->
    <g id="main-content" transform="translate(240, 0)">
         <!-- Top Header -->
         <g id="top-header">
             <rect x="0" y="0" width="1200" height="60" fill="var(--header-bg)" stroke="var(--border-color)" stroke-width="1"/>
             <g id="header-actions" transform="translate(1180, 30)"> <!-- Position actions relative to center y=30 -->
                 <!-- Logout Icon -->
                 <text x="0" y="6" class="text-end text-lg" style="cursor: pointer;">➡️</text>
                 <!-- User Menu -->
                 <g id="user-menu" transform="translate(-40, 0)" style="cursor: pointer;">
                     <text x="0" y="6" class="text-end text-small font-medium">▼</text>
                     <text x="-15" y="6" class="text-end text-base font-medium">Robert Bravery</text>
                     <circle cx="-125" cy="0" r="16" fill="var(--secondary-color)"/>
                     <text x="-125" y="6" class="text-center text-white text-small font-semibold">RB</text>
                 </g>
                  <!-- Notification Icon -->
                 <text x="-165" y="6" class="text-end text-lg" style="cursor: pointer;">🔔</text>
                 <circle cx="-160" cy="-5" r="6" fill="red"/>
                 <text x="-160" y="-1" class="text-center text-white" style="font-size: 9px;">1</text>
             </g>
         </g>

         <!-- Page Content - Showing Client List View -->
         <g id="page-content" transform="translate(25, 85)"> <!-- Padding left and below header -->

             <!-- Section: List Clients -->
             <g id="list-clients-view">
                 <!-- Page Header -->
                <text x="0" y="0" class="text-xl font-semibold">Client Directory</text>
                <text x="0" y="25" class="text-base" fill="#777">View and manage all your clients.</text>

                <!-- Data Table Container -->
                 <g id="client-table-container" transform="translate(0, 55)">
                     <rect x="0" y="0" width="1150" height="250" fill="var(--card-bg)" stroke="var(--border-color)" rx="6" ry="6"/>

                     <!-- Table Header -->
                     <rect x="1" y="1" width="1148" height="45" fill="var(--table-header-bg)"/>
                     <text x="20" y="28" class="text-small font-semibold uppercase" fill="var(--table-header-text)">Client Name</text>
                     <text x="250" y="28" class="text-small font-semibold uppercase" fill="var(--table-header-text)">Email</text>
                     <text x="500" y="28" class="text-small font-semibold uppercase" fill="var(--table-header-text)">Phone</text>
                     <text x="700" y="28" class="text-small font-semibold uppercase" fill="var(--table-header-text)">Pets</text>
                     <text x="1130" y="28" class="text-small font-semibold uppercase text-end" fill="var(--table-header-text)">Actions</text>
                     <line x1="1" y1="46" x2="1149" y2="46" stroke="var(--border-color)"/>

                     <!-- Table Body Rows -->
                     <g id="client-row-1" transform="translate(0, 46)">
                          <text x="20" y="28" class="text-base">Caroline Wessinger</text>
                          <text x="250" y="28" class="text-base"><EMAIL></text>
                          <text x="500" y="28" class="text-base">(123) 456-7890</text>
                          <text x="700" y="28" class="text-base">2</text>
                          <g class="actions" transform="translate(1130, 28)">
                             <text x="0" y="0" class="text-end text-lg action-icon" style="cursor: pointer;" fill="var(--primary-color)">✏️</text>
                             <text x="-35" y="0" class="text-end text-lg action-icon" style="cursor: pointer;" fill="var(--primary-color)">+🐾</text>
                             <text x="-70" y="0" class="text-end text-lg action-icon" style="cursor: pointer;" fill="var(--primary-color)">👁️</text>
                          </g>
                          <line x1="1" y1="46" x2="1149" y2="46" stroke="var(--border-color)"/>
                     </g>
                     <g id="client-row-2" transform="translate(0, 92)">
                          <text x="20" y="28" class="text-base">Richard Boyder</text>
                          <text x="250" y="28" class="text-base"><EMAIL></text>
                          <text x="500" y="28" class="text-base">(987) 654-3210</text>
                          <text x="700" y="28" class="text-base">1</text>
                           <g class="actions" transform="translate(1130, 28)">
                             <text x="0" y="0" class="text-end text-lg action-icon" style="cursor: pointer;" fill="var(--primary-color)">✏️</text>
                             <text x="-35" y="0" class="text-end text-lg action-icon" style="cursor: pointer;" fill="var(--primary-color)">+🐾</text>
                             <text x="-70" y="0" class="text-end text-lg action-icon" style="cursor: pointer;" fill="var(--primary-color)">👁️</text>
                          </g>
                          <line x1="1" y1="46" x2="1149" y2="46" stroke="var(--border-color)"/>
                     </g>
                      <g id="client-row-3" transform="translate(0, 138)">
                          <text x="20" y="28" class="text-base">Ashleigh Moodley</text>
                          <text x="250" y="28" class="text-base"><EMAIL></text>
                          <text x="500" y="28" class="text-base">(555) 111-2222</text>
                          <text x="700" y="28" class="text-base">0</text>
                           <g class="actions" transform="translate(1130, 28)">
                             <text x="0" y="0" class="text-end text-lg action-icon" style="cursor: pointer;" fill="var(--primary-color)">✏️</text>
                             <text x="-35" y="0" class="text-end text-lg action-icon" style="cursor: pointer;" fill="var(--primary-color)">+🐾</text>
                             <text x="-70" y="0" class="text-end text-lg action-icon" style="cursor: pointer;" fill="var(--primary-color)">👁️</text>
                          </g>
                          <!-- No bottom line for last row in this view -->
                     </g>
                 </g>

                 <!-- Pagination Placeholder -->
                 <g id="pagination" transform="translate(1150, 325)"> <!-- Position relative to table bottom right -->
                      <text x="0" y="0" class="text-end text-small" fill="#777">&gt;</text>
                      <rect x="-15" y="-10" width="10" height="10" fill="#ccc" style="cursor: not-allowed;"/> <!-- Disabled Button Placeholder -->
                      <text x="-20" y="0" class="text-end text-small" fill="#aaa">&lt;</text>
                      <text x="-50" y="0" class="text-end text-small" fill="#777">1 - 3 of 3</text>
                      <rect x="-140" y="-12" width="40" height="15" stroke="var(--border-color)" fill="white"/> <!-- Select Placeholder -->
                      <text x="-135" y="0" class="text-small" fill="#555">10 ▼</text>
                       <text x="-150" y="0" class="text-end text-small" fill="#777">Items per page:</text>
                 </g>
             </g>

              <!-- Add other views like add-client, view-client here if needed, -->
              <!-- positioning them off-canvas or managing visibility via layers in Penpot -->

         </g>
    </g>
</svg>