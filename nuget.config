﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <packageRestore>
        <add key="enabled" value="True" />
        <add key="automatic" value="True" />
    </packageRestore>

    <packageSources>
        <clear />
        <add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
        <add key="LocalNuget" value="c:\LocalNuget" />

    </packageSources>

    <packageSourceMapping>
        <packageSource key="nuget.org">
            <package pattern="*" />
            <package pattern="System*" />
        </packageSource>
    </packageSourceMapping>

    <bindingRedirects>
        <add key="skip" value="False" />
    </bindingRedirects>

    <packageManagement>
        <add key="format" value="0" />
        <add key="disabled" value="False" />
    </packageManagement>
</configuration>