{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ApiBaseUrl": "https://localhost:57679", "RandomDogImage": "https://dog.ceo/api/breeds/image/random", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/blazorui-log-.txt", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}", "rollOnFileSizeLimit": true, "fileSizeLimitBytes": 10485760}}], "Enrich": ["FromLogContext", "WithMachineName"]}}