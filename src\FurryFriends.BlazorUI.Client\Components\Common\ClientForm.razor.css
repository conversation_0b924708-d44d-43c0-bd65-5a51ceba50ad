/* Form Sections */
.form-section {
    margin-bottom: 0.5rem;
    padding-bottom: .75rem;
    border-bottom: 1px solid #eaedf2;
}

.form-section:last-of-type {
    border-bottom: none;
    padding-bottom: 0;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 500;
    color: #444;
    margin-bottom: .75rem;
    padding-bottom: 0.5rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: #4A90E2;
    border-radius: 3px;
}

/* Form Grid and Layout */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr 1fr));
    gap: .75rem;
}

.form-group {
    margin-bottom: 0.75rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.95rem;
    color: #444;
}

.required {
    color: #dc3545;
    margin-left: 2px;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    box-sizing: border-box;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.25);
}

.form-control::placeholder {
    color: #aaa;
    font-size: 0.9rem;
}

.form-control.invalid {
    border-color: #dc3545;
}

.form-control.invalid:focus {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
}

/* Validation */
.validation-message {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: block;
}

/* Special Form Layouts */
.email-phone-container {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: 3fr 1fr 2fr;
    gap: 1rem;
}

.notes-full-width {
    grid-column: 1 / -1;
}

/* Buttons */
.button-row {
    display: flex;
    justify-content: flex-end;
    margin-top: 2rem;
    gap: 1rem;
    padding-top: 1.5rem;
    border-top: none; /* Override the default border-top */
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background-color: #4A90E2;
    color: white;
}

.btn-primary:hover {
    background-color: #3a7bc8;
    transform: translateY(-1px);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-secondary {
    background-color: #f0f0f0;
    color: #333;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
}

.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Error Messages */
.api-error-message {
    background-color: #fff5f5;
    border: 1px solid #ffcccc;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: #dc3545;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}

.error-icon {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Deep Validation Styles */
::deep .invalid {
    border-color: #dc3545 !important;
}

::deep .modified.invalid {
    border-color: #dc3545 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
}

::deep .valid {
    border-color: #28a745 !important;
}

::deep .validation-message {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: block;
}

/* Style InputText and InputTextArea components */
::deep .form-control.invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

::deep .form-control.valid {
    border-color: #28a745 !important;
}

/* User Preferences */
.no-underline {
    text-decoration: none;
}

.no-underline:hover {
    text-decoration: none;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 1.1rem;
    }

    .form-section {
        margin-bottom: .5rem;
        padding-bottom: 1rem;
    }

    .button-row {
        flex-direction: column-reverse;
        padding-top: 1rem;
    }

    .button-row button {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .form-control {
        padding: 0.6rem 0.8rem;
    }
}
