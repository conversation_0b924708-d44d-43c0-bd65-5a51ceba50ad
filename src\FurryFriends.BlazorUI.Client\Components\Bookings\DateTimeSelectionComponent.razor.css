.datetime-selection-container {
    background: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.selection-header {
    text-align: center;
    margin-bottom: 25px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
}

.selection-header h4 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-weight: 600;
}

.instruction-text {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.selection-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
}

.time-selection-section {
    margin-top: 25px;
}

.loading-slots {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    color: #6c757d;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-spinner.small {
    width: 20px;
    height: 20px;
    border-width: 2px;
    margin: 0 10px 0 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.time-slots-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.time-slot-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.time-slot-option:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
    transform: translateY(-1px);
}

.time-slot-option.selected {
    border-color: #28a745;
    background: #f8fff9;
    box-shadow: 0 2px 8px rgba(40,167,69,0.2);
}

.slot-time {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 8px;
}

.slot-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
}

.duration {
    color: #6c757d;
}

.price {
    color: #28a745;
    font-weight: 600;
}

.no-slots-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-slots-message i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #dee2e6;
}

.no-slots-message p {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.no-slots-message small {
    font-size: 0.9rem;
}

.custom-time-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.form-check {
    margin-bottom: 15px;
}

.form-check-input {
    margin-right: 8px;
}

.form-check-label {
    color: #495057;
    font-weight: 500;
}

.custom-time-inputs {
    margin-top: 15px;
}

.custom-time-summary {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.custom-time-summary .duration {
    color: #6c757d;
    font-weight: normal;
}

.selection-summary {
    margin-top: 30px;
    padding: 20px;
    background: #f8fff9;
    border: 1px solid #d4edda;
    border-radius: 8px;
}

.selection-summary h5 {
    color: #155724;
    margin-bottom: 15px;
    font-weight: 600;
}

.summary-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-item .label {
    font-weight: 600;
    color: #495057;
}

.summary-item .value {
    color: #2c3e50;
}

.summary-item .value.price {
    color: #28a745;
    font-weight: 600;
    font-size: 1.1rem;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 8px;
    padding: 8px 12px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
    .datetime-selection-container {
        padding: 20px;
    }
    
    .time-slots-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .time-slot-option {
        padding: 12px;
    }
    
    .slot-time {
        font-size: 1rem;
    }
    
    .summary-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .time-slots-container {
        grid-template-columns: 1fr;
    }
    
    .custom-time-inputs .row {
        margin: 0;
    }
    
    .custom-time-inputs .col-md-6 {
        padding: 0;
        margin-bottom: 15px;
    }
}
