@page "/createclient"
@rendermode InteractiveAuto
@using FurryFriends.BlazorUI.Client.Components.Common

<div class="create-client-container">
	<h3>Add New Client</h3>
	<p class="form-description">Enter the details for the new client.</p>

	<ClientForm
		ClientModel="clientModel"
		OnSubmit="HandleSaveClient"
		OnCancel="HandleCancel"
		FormName="CreateClient"
		Enhance="true"
		IsSubmitting="isSubmitting"
		ErrorMessage=""
		SubmitButtonText="Save Client" />
</div>


